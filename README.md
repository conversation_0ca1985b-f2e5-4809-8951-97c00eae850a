# Playwright 自动化测试项目

基于 Python Playwright 的自动化测试框架，支持 Web 界面测试和 API 接口测试。

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
```

### 2. 安装浏览器
```bash
playwright install
```

### 3. 运行测试

#### 🌐 Web 控制台
```bash
# 启动 Web 控制台
python start_dashboard.py

# 访问 http://localhost:8501
```

#### 🔧 直接使用 pytest
```bash
# 运行特定测试
pytest tests/web/test_baidu_search.py::TestBaiduSearch::test_baidu_search_123 -v -s

# 运行所有测试
pytest tests/ -v
```

## 项目结构
```
PlayWright/
├── tests/                  # 测试用例目录
│   ├── web/               # Web 界面测试
│   │   ├── test_baidu_search.py    # 百度搜索测试
│   │   ├── test_simple_example.py  # 简单示例测试
│   │   └── test_data_driven.py     # 数据驱动测试
│   └── conftest.py        # pytest 配置
├── fixtures/              # 测试数据
│   ├── test_data.json     # JSON 格式测试数据
│   └── sample.txt         # 示例文件
├── reports/               # 测试报告输出
├── requirements.txt       # Python 依赖
├── pytest.ini           # pytest 配置
├── test_dashboard.py     # Web 测试控制台
└── start_dashboard.py    # 控制台启动器
```

## 测试用例

### 百度搜索测试 (test_baidu_search.py)
- **符合 Playwright 最佳实践**
- 使用 `expect()` 断言而非 `assert`
- 直接使用 Playwright API，无需自定义工具函数
- 自动等待，无需手动处理时间

### 简单示例测试 (test_simple_example.py)
- 表单交互测试
- 元素状态验证
- 基础操作演示

### API 测试示例 (test_api_example.py)
- HTTP 请求测试示例

## 特性
- **🌐 Web 控制台**：可视化测试管理界面
- **📁 文件选择**：图形化选择测试文件和方法
- **⚙️ 配置选项**：浏览器、执行模式等可视化配置
- **📊 报告管理**：在线查看和管理测试报告
- **🎯 精确控制**：支持单个测试方法执行
- **符合官方最佳实践**：按照 Playwright 官方文档编写
- **自动等待**：无需手动处理超时和竞态条件
- **语义化断言**：使用 `expect()` API
- **简洁代码**：直接使用 Playwright 内置功能
- **多浏览器支持**：Chromium、Firefox、WebKit
- **HTML 测试报告**：详细的测试结果
- **截图功能**：失败时自动截图

## Web 控制台功能
- **测试文件管理**：自动发现和列出所有测试文件
- **测试方法选择**：解析并显示测试类和方法
- **浏览器配置**：支持 Chrome、Firefox、WebKit 等
- **执行选项**：无头模式、并行执行、失败重试等
- **实时输出**：显示测试执行过程和结果
- **报告查看**：在线浏览历史测试报告
- **一键清理**：清理旧的测试报告文件
