"""
数据驱动测试示例 - 使用 fixtures 数据
"""
import json
import pytest
import re
from playwright.sync_api import Page, expect


def load_test_data():
    """加载测试数据"""
    with open("fixtures/test_data.json", "r", encoding="utf-8") as f:
        return json.load(f)


class TestDataDriven:
    """数据驱动测试类"""
    
    @pytest.mark.parametrize("keyword", load_test_data()["search_keywords"])
    def test_baidu_search_multiple_keywords(self, page: Page, keyword):
        """使用多个关键词测试百度搜索"""
        
        test_data = load_test_data()
        
        # 导航到百度
        page.goto(test_data["urls"]["baidu"])
        
        # 验证页面标题
        expect(page).to_have_title(re.compile("百度"))
        
        # 搜索关键词
        search_input = page.locator("#kw")
        search_input.fill(keyword)
        
        search_button = page.locator("#su")
        search_button.click()
        
        # 验证搜索结果
        expect(page).to_have_url(re.compile(f".*[?&]wd={keyword}.*"))
        expect(page.locator("#content_left")).to_be_visible()
        
        # 验证页面标题包含关键词
        expect(page).to_have_title(re.compile(keyword))
    
    
    def test_with_user_data(self, page: Page):
        """使用用户数据的测试示例"""
        
        test_data = load_test_data()
        user = test_data["user_data"]["valid_user"]
        
        # 这里可以使用用户数据进行登录测试等
        # 示例：验证用户名格式
        assert len(user["username"]) > 0
        assert len(user["password"]) >= 8
    
    
    def test_expected_results(self, page: Page):
        """使用预期结果数据的测试"""
        
        test_data = load_test_data()
        expected = test_data["expected_results"]["baidu_search_123"]
        
        # 执行搜索
        page.goto(test_data["urls"]["baidu"])
        page.locator("#kw").fill("123")
        page.locator("#su").click()
        
        # 使用预期结果验证
        search_results = page.locator(".result, .c-container")
        
        # 验证结果数量至少达到预期
        actual_count = search_results.count()
        assert actual_count >= expected["min_results"], f"搜索结果数量 {actual_count} 少于预期 {expected['min_results']}"
        
        # 验证标题包含预期内容
        expect(page).to_have_title(re.compile(expected["title_contains"]))
