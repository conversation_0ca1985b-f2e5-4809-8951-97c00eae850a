"""
简单的 Playwright 测试示例 - 完全按照官方最佳实践
"""
import re
from playwright.sync_api import Page, expect



def test_baidu_simple_search(page: Page):
    """简单的百度搜索测试"""
    
    # 导航到百度
    page.goto("https://www.baidu.com")
    
    # 验证页面标题
    expect(page).to_have_title(re.compile("百度"))
    
    # 在搜索框输入并搜索
    page.locator("#kw").fill("123")
    page.locator("#su").click()
    
    # 验证搜索结果页面
    expect(page).to_have_url(re.compile(r".*[?&]wd=123.*"))
    expect(page.locator("#content_left")).to_be_visible()


def test_form_interaction(page: Page):
    """表单交互测试示例"""
    
    # 导航到测试页面
    page.goto("https://www.baidu.com")
    
    # 定位搜索框
    search_box = page.locator("#kw")
    
    # 填写内容
    search_box.fill("Playwright")
    
    # 验证输入值
    expect(search_box).to_have_value("Playwright")
    
    # 清空并重新填写
    search_box.clear()
    search_box.fill("Python")
    
    # 验证新值
    expect(search_box).to_have_value("Python")


def test_element_states(page: Page):
    """元素状态测试"""
    
    page.goto("https://www.baidu.com")
    
    # 验证元素可见
    expect(page.locator("#kw")).to_be_visible()
    expect(page.locator("#su")).to_be_visible()
    
    # 验证元素启用
    expect(page.locator("#kw")).to_be_enabled()
    expect(page.locator("#su")).to_be_enabled()
    
    # 验证元素可编辑
    expect(page.locator("#kw")).to_be_editable()
