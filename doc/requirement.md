# Playwright 自动化测试项目

## 项目概述
基于 Python Playwright 的自动化测试框架，支持 Web 界面测试和 API 接口测试。

## 项目结构
```
PlayWright/
├── tests/                  # 测试用例目录
│   ├── web/               # Web 界面测试
│   │   └── test_baidu_search.py  # 百度搜索测试
│   ├── api/               # API 接口测试
│   │   └── test_api_example.py   # API 测试示例
│   ├── __init__.py
│   └── conftest.py        # pytest 配置
├── utils/                 # 工具函数
│   ├── __init__.py
│   └── helpers.py         # 通用工具函数
├── fixtures/              # 测试数据
├── reports/               # 测试报告输出
├── doc/                   # 文档
├── requirements.txt       # Python 依赖
├── pytest.ini           # pytest 配置
└── run_tests.py          # 测试执行脚本
```

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
```

### 2. 安装浏览器
```bash
playwright install
```

### 3. 运行测试

#### 运行所有测试
```bash
python run_tests.py --type all
```

#### 运行 Web 测试
```bash
python run_tests.py --type web
```

#### 运行 API 测试
```bash
python run_tests.py --type api
```

#### 运行百度搜索测试
```bash
python run_tests.py --type baidu
```

### 4. 直接使用 pytest
```bash
# 运行所有测试
pytest tests/

# 运行特定测试文件
pytest tests/web/test_baidu_search.py

# 运行特定测试方法
pytest tests/web/test_baidu_search.py::TestBaiduSearch::test_baidu_search_123

# 生成 HTML 报告
pytest tests/ --html=reports/report.html --self-contained-html
```

## 测试用例说明

### 百度搜索测试 (test_baidu_search.py)
- `test_baidu_search_123`: 在百度搜索 "123" 并验证结果
- `test_baidu_search_suggestions`: 测试搜索建议功能
- `test_baidu_homepage_elements`: 验证百度首页关键元素

### API 测试示例 (test_api_example.py)
- `test_get_request`: 测试 GET 请求
- `test_post_request`: 测试 POST 请求
- `test_response_headers`: 测试响应头

## 工具函数 (utils/helpers.py)
- `wait_for_element`: 等待元素出现
- `take_screenshot`: 截图功能
- `wait_and_click`: 等待并点击
- `wait_and_fill`: 等待并填写
- `verify_text_present`: 验证文本存在
- `navigate_to_url`: 导航到URL

## 测试报告
测试执行后会在 `reports/` 目录生成 HTML 格式的测试报告，包含：
- 测试结果统计
- 失败测试详情
- 截图（如果有）
- 执行时间等信息

## 配置说明

### pytest.ini
- 配置测试路径、浏览器、报告格式等
- 定义测试标记（web、api、smoke、regression）

### conftest.py
- 浏览器上下文配置
- 页面设置 fixture
- API 测试上下文

## 扩展测试用例
1. 在 `tests/web/` 或 `tests/api/` 目录创建新的测试文件
2. 继承相应的测试基类
3. 使用 `@pytest.mark.web` 或 `@pytest.mark.api` 标记
4. 利用 `utils/helpers.py` 中的工具函数

## 注意事项
- 确保网络连接正常
- 某些网站可能有反爬虫机制
- 建议在稳定的网络环境下运行测试
- 可以通过 `--headless` 参数在无头模式下运行