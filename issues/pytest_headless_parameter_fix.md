# Playwright pytest --headless 参数修复任务

## 问题描述
用户在使用 Web 控制台运行测试时，遇到 `--headless` 参数错误：
```
pytest: error: unrecognized arguments: --headless
```

## 根本原因
`test_dashboard.py` 文件第78行错误地将 `--headless` 参数直接传递给 pytest 命令，但 pytest 不认识这个参数。正确的做法是通过 Playwright 的配置来控制无头模式。

## 解决方案
采用方案1：修改 conftest.py 配置，通过环境变量或命令行参数控制 Playwright 的 headless 模式。

## 执行计划
1. 修改 conftest.py 配置支持动态 headless 控制
2. 修复 test_dashboard.py 的错误参数传递
3. 添加自定义 pytest 参数支持
4. 更新 pytest.ini 配置
5. 测试验证

## 开始时间
2025-07-30

## 修复详情

### 1. 修改 conftest.py
- 添加 `pytest_addoption` 函数支持 `--playwright-headless` 参数
- 修改 `browser_type_launch_args` fixture 支持动态 headless 控制
- 实现优先级：命令行参数 > 环境变量 > 默认值
- 添加异常处理和参数验证

### 2. 修复 test_dashboard.py
- 移除错误的 `--headless` 参数传递
- 改为通过环境变量 `PLAYWRIGHT_HEADLESS` 控制
- 修改函数返回值包含环境变量
- 在 subprocess.run 中传递环境变量

### 3. 代码优化
- 添加详细的文档注释
- 增强参数验证逻辑
- 改进错误处理机制

## 使用方法

### 1. Web 控制台
- 在界面中勾选"无头模式"复选框即可控制

### 2. 命令行方式
```bash
# 无头模式
pytest tests/ --playwright-headless

# 有头模式（默认）
pytest tests/
```

### 3. 环境变量方式
```bash
# 无头模式
PLAYWRIGHT_HEADLESS=true pytest tests/

# 有头模式
PLAYWRIGHT_HEADLESS=false pytest tests/
```

## 完成时间
2025-07-30

## 状态
✅ 已完成并优化
