# Pytest --headed 参数错误修复

## 问题描述
用户在运行 pytest 时遇到错误：
```
ERROR: usage: pytest [options] [file_or_dir] [file_or_dir] [...]
pytest: error: argument --headed: ignored explicit argument 'false'
```

## 问题原因
在 `test_dashboard.py` 文件中，第 63 行使用了错误的参数格式：
```python
if headless:
    cmd.append("--headed=false")  # ❌ 错误：--headed 不接受值
```

Playwright pytest 插件中：
- `--headed` 是一个布尔标志，不接受参数值
- 无头模式是默认行为，不需要特殊参数
- 有头模式需要显式添加 `--headed` 参数

## 解决方案
修改 `test_dashboard.py` 第 61-65 行的逻辑：

**修复前：**
```python
# 显示模式
if headless:
    cmd.append("--headed=false")  # ❌ 错误
else:
    cmd.append("--headed")
```

**修复后：**
```python
# 显示模式
if not headless:
    cmd.append("--headed")
# 无头模式不需要添加参数，这是默认行为
```

## 修复结果
- ✅ 无头模式：`pytest tests/` （不添加任何头部相关参数）
- ✅ 有头模式：`pytest tests/ --headed`
- ✅ pytest 命令可以正常执行
- ✅ 测试收集功能正常工作

## 验证
执行 `pytest tests/ --collect-only` 成功收集到 12 个测试用例，无错误信息。

## 修复时间
2025-07-30

## 状态
✅ 已完成
