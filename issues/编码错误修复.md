# Windows 系统 subprocess 编码错误修复

## 任务背景
用户遇到 Python 编码错误：
```
UnicodeDecodeError: 'gbk' codec can't decode byte 0xaa in position 2261: illegal multibyte sequence
```

## 问题分析
这是 Windows 系统下 subprocess.run() 调用时的典型编码问题：
1. Windows 系统默认使用 GBK 编码
2. subprocess 输出包含非 GBK 编码的字符
3. `text=True` 参数使用系统默认编码解码，导致失败

## 解决方案
采用方案1：在所有 subprocess.run() 调用中指定 UTF-8 编码

## 修改内容

### 1. test_dashboard.py
**位置**：第236-242行
**修改前**：
```python
result = subprocess.run(
    cmd, 
    capture_output=True, 
    text=True, 
    timeout=300,
    cwd=os.getcwd()
)
```

**修改后**：
```python
result = subprocess.run(
    cmd, 
    capture_output=True, 
    text=True, 
    encoding='utf-8',
    timeout=300,
    cwd=os.getcwd()
)
```

### 2. start_dashboard.py
**位置1**：第39行
**修改前**：
```python
subprocess.run(cmd, check=True)
```

**修改后**：
```python
subprocess.run(cmd, check=True, encoding='utf-8')
```

**位置2**：第56-62行
**修改前**：
```python
subprocess.run([
    sys.executable, "-m", "streamlit", "run",
    "test_dashboard.py",
    "--server.port", "8501",
    "--server.address", "localhost",
    "--server.headless", "true"
])
```

**修改后**：
```python
subprocess.run([
    sys.executable, "-m", "streamlit", "run",
    "test_dashboard.py",
    "--server.port", "8501",
    "--server.address", "localhost",
    "--server.headless", "true"
], encoding='utf-8')
```

## 预期效果
1. 解决 UnicodeDecodeError: 'gbk' codec can't decode byte 错误
2. 所有 subprocess 输出正确以 UTF-8 编码处理
3. 测试执行和控制台启动功能正常工作
4. 兼容 Windows 系统的编码环境

## 额外修复：无头模式参数错误

### 问题发现
用户指出无头模式应该使用 `--headless` 参数，而不是 `--headed`。

### 修复内容
**位置**：test_dashboard.py 第76-79行
**修改前**：
```python
if not headless:
    cmd.append("--headed")  # ❌ 错误逻辑
```

**修改后**：
```python
if headless:
    cmd.append("--headless")  # ✅ 正确逻辑
```

### 3. pytest.ini 配置修复
**位置**：pytest.ini 第6行
**修改前**：
```ini
addopts =
    --browser chromium
    --browser-channel chrome
    --headed
    --html=reports/report.html
    --self-contained-html
    --tb=short
    -v
```

**修改后**：
```ini
addopts =
    --browser chromium
    --browser-channel chrome
    --html=reports/report.html
    --self-contained-html
    --tb=short
    -v
```

## 测试验证
启动测试控制台验证修复效果：
```bash
python start_dashboard.py
# 访问 http://localhost:8501
```

执行测试验证编码处理和无头模式：
```bash
# 有头模式测试
pytest tests/web/test_simple_example.py -v

# 无头模式测试
pytest tests/web/test_simple_example.py -v --headless
```
