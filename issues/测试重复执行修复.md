# Playwright 测试重复执行问题修复

## 问题描述
用户发现 Playwright 测试会执行两次，显示为：
- `test_example[chromium0]` 
- `test_example[chromium1]`

## 问题分析
通过分析发现问题根源是 **pytest-xdist 插件的默认并行行为**：

1. **pytest-xdist 插件**：项目中安装了 `pytest-xdist` 插件用于并行测试
2. **默认行为**：当没有明确指定 `--numprocesses` 参数时，插件可能会创建多个工作进程
3. **参数化效果**：每个工作进程都会执行测试，导致测试被参数化为多个实例

## 解决方案
在 `pytest.ini` 配置文件中添加 `--numprocesses=1` 参数，强制使用单一进程执行测试。

## 修复详情

### 修改前的 pytest.ini：
```ini
[pytest]
testpaths = tests
addopts =
    --browser chromium
    --browser-channel chrome
    --html=reports/report.html
    --self-contained-html
    --tb=short
    -v
```

### 修改后的 pytest.ini：
```ini
[pytest]
testpaths = tests
addopts =
    --browser chromium
    --browser-channel chrome
    --numprocesses=1
    --html=reports/report.html
    --self-contained-html
    --tb=short
    -v
```

## 验证结果

### 修复前：
```
test_example[chromium0] PASSED
test_example[chromium1] PASSED
2 passed
```

### 修复后：
```
test_example[chromium] PASSED
1 passed
```

## 技术说明

1. **--numprocesses=1**：强制 pytest-xdist 使用单一工作进程
2. **保持功能**：不影响其他测试功能和配置
3. **性能影响**：对于单个测试，性能影响微乎其微
4. **并行选项**：如需并行测试，可以通过命令行覆盖：`pytest -n auto`

## 完成时间
2025-07-30

## 状态
✅ 已完成并验证
